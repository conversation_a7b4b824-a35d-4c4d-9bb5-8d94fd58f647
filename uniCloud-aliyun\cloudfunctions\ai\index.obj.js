// AI 云函数主文件 - 重构版本
// 提供智能任务分析和执行功能的核心 API 接口

const OpenAI = require('openai')

// 引入模块化组件
const { doubaoParams, DEFAULT_SYSTEM_PROMPT } = require('./modules/config')
const { ExecutionContextManager } = require('./modules/context')
const { IntelligentExecutionPlanner } = require('./modules/planner')
const { executeRobustPlan } = require('./modules/executor')
const { globalPerformanceMonitor } = require('./modules/performance')

module.exports = {
  _before: function () {
    // 通用预处理器
  },

  /**
   * 与 DeepSeek AI 进行聊天对话
   */
  async speak() {
    const httpInfo = this.getHttpInfo()
    let {
      message,
      history_records = [],
      model = 'deepseek-chat',
      system = 'You are a helpful assistant.',
    } = JSON.parse(httpInfo.body)

    if (!message) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: '消息内容不能为空',
      }
    }

    try {
      const openai = new OpenAI({
        baseURL: 'https://api.deepseek.com/v1',
        apiKey: '***********************************',
      })

      const messages = [{ role: 'system', content: system }, ...history_records]
      if (message) messages.push({ role: 'user', content: message })

      const completion = await openai.chat.completions.create({
        messages,
        model: model,
      })

      return {
        content: completion.choices[0].message.content,
      }
    } catch (error) {
      return {
        errCode: 'API_ERROR',
        errMsg: error.message || '调用 AI 接口失败',
      }
    }
  },

  /**
   * 基于 SSE Channel 的流式聊天接口 - 实时推送 AI 响应
   *
   * 核心功能流程：
   * 1. 接收用户消息，通过豆包 AI 进行意图识别
   * 2. 实时推送 AI 响应内容到前端
   * 3. 解析意图类型和内容，触发相应的任务执行
   * 4. 支持任务创建 (create_task)、任务查找 (find_task)、普通聊天 (chat)
   *
   * 技术特性：
   * - 意图识别：解析 AI 返回的「意图类型」和「意图内容」
   * - 任务执行：非 chat 类型会触发智能执行计划
   * - 实时推送：通过 SSE Channel 推送执行过程和结果
   * - 性能监控：集成性能监控和错误处理机制
   *
   * @param {Object} params - 参数对象
   * @param {string} params.message - 用户输入的消息内容
   * @param {Array} params.messages - 历史对话记录数组，格式：[{role, content}]
   * @param {string} params.model - AI 模型名称，默认'doubao-seed-1-6-250615'
   * @param {string} params.system - 系统提示词，默认使用意图识别提示词
   * @param {Object} params.channel - SSE Channel 对象，用于实时推送
   *
   * @returns {Object} 返回结果对象
   * @returns {number} returns.errCode - 错误码，0 表示成功
   * @returns {string} returns.errMsg - 错误信息或成功标识
   * @returns {Object} returns.data - 响应数据
   * @returns {string} returns.data.type - 响应类型：'task_executed' | 'stream_complete'
   * @returns {string} returns.data.intentType - 识别的意图类型
   * @returns {string} returns.data.content - AI 回复内容
   * @returns {number} returns.data.totalChunks - 推送的数据块总数
   * @returns {Object} [returns.data.executionPlan] - 执行计划（任务执行时）
   * @returns {Array} [returns.data.contextData] - 上下文数据键列表（任务执行时）
   * @returns {number} [returns.data.executionTime] - 执行耗时（任务执行时）
   * @returns {Object} [returns.data.performanceReport] - 性能报告（任务执行时）
   */
  async chatStreamSSE(params) {
    let {
      message,
      messages: history_records = [],
      model = 'doubao-seed-1-6-250615',
      system = DEFAULT_SYSTEM_PROMPT,
      channel,
    } = params

    console.log('SSE 流式聊天消息：', message)

    // 参数验证：确保必需参数存在
    if (!message) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: '消息内容不能为空',
      }
    }

    if (!channel) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: 'SSE Channel 不能为空',
      }
    }

    try {
      // 反序列化 SSE Channel，用于实时推送数据到前端
      const sseChannel = uniCloud.deserializeSSEChannel(channel)

      // 初始化豆包 AI 客户端
      const openai = new OpenAI(doubaoParams)

      // 构建对话消息数组：系统提示词 + 历史记录 + 当前用户消息
      const messages = [{ role: 'system', content: system }, ...history_records]
      if (message) messages.push({ role: 'user', content: message })

      // 推送开始消息，通知前端开始处理
      await sseChannel.write({
        type: 'start',
        message: '开始生成回复...',
        timestamp: Date.now(),
      })

      // 创建流式 AI 响应，设置 5 分钟超时
      const streamResponse = await openai.chat.completions.create({
        messages,
        model: model,
        stream: true, // 启用流式响应
        timeout: 300000, // 5 分钟超时（毫秒）
      })

      // 初始化流式处理相关变量
      let fullContent = '' // 累积的完整 AI 响应内容
      let chunkCount = 0 // 推送的数据块计数
      let intentType = null // 识别的意图类型
      let isIntentContentStarted = false // 是否开始推送意图内容
      let intentContent = '' // 提取的意图内容

      // 正则表达式：匹配 AI 返回的意图类型和内容
      // 意图类型格式：「意图类型」：create_task|find_task|chat
      const intentTypeRegex = /「意图类型」：(create_task|find_task|chat)/
      // 意图内容格式：「意图内容」：具体内容...
      const intentContentRegex = /「意图内容」：([\s\S]*)/

      // 流式处理 AI 响应数据
      for await (const chunk of streamResponse) {
        const content = chunk.choices[0]?.delta?.content || ''
        if (content) {
          fullContent += content // 累积完整内容
          chunkCount++ // 增加数据块计数

          // 第一阶段：检测意图类型
          if (!intentType) {
            const typeMatch = intentTypeRegex.exec(fullContent)
            if (typeMatch) {
              intentType = typeMatch[1] // 提取意图类型：create_task|find_task|chat
              console.log(`检测到意图类型：${intentType}`)

              // 推送意图类型到前端
              await sseChannel.write({
                type: 'intent_type',
                intentType: intentType,
                timestamp: Date.now(),
              })
              continue // 跳过当前块的推送，避免重复发送
            }
          }

          // 第二阶段：检测意图内容开始
          if (intentType && !isIntentContentStarted) {
            const contentMatch = intentContentRegex.exec(fullContent)
            if (contentMatch) {
              isIntentContentStarted = true
              intentContent = contentMatch[1] // 提取意图内容
              console.log('检测到意图内容开始')

              // 推送意图内容开始标识
              await sseChannel.write({
                type: 'intent_content_start',
                content: intentContent,
                timestamp: Date.now(),
              })
              continue // 跳过当前块的推送
            }
          } else if (isIntentContentStarted) {
            // 第三阶段：持续推送意图内容块
            await sseChannel.write({
              type: 'intent_content_chunk',
              content: content,
              timestamp: Date.now(),
            })
            intentContent += content // 累积意图内容
            console.log(`推送意图内容块：${content}`)
          } else {
            // 尚未检测到完整的意图格式，继续累积内容
            console.log(`累积内容：${fullContent}`)
          }
        }
      }

      // 调试输出：打印完整的处理结果
      console.log('AI 完整返回内容：', fullContent)
      console.log('提取的意图类型：', intentType)
      console.log('提取的意图内容：', intentContent)

      // 任务执行阶段：非聊天类型触发智能任务执行
      if (intentType && intentType !== 'chat') {
        // 创建执行上下文管理器，用于管理执行过程中的数据
        const context = new ExecutionContextManager(IntelligentExecutionPlanner.generateUUID(), message)

        // 使用智能执行计划生成器创建执行计划
        const executionPlan = await IntelligentExecutionPlanner.generatePlan(message, intentType)

        // 如果生成了有效的执行计划，则执行任务
        if (executionPlan.totalSteps > 0) {
          // 使用增强的执行引擎执行计划，集成错误处理和性能监控
          await executeRobustPlan(executionPlan, context, sseChannel, globalPerformanceMonitor)

          return {
            errCode: 0,
            errMsg: 'success',
            data: {
              type: 'task_executed',
              intentType: intentType,
              executionPlan: executionPlan,
              contextData: Array.from(context.contextData.keys()),
              executionTime: executionPlan.totalExecutionTime,
              performanceReport: globalPerformanceMonitor.getPerformanceReport(),
              content: isIntentContentStarted ? intentContent : fullContent,
              totalChunks: chunkCount,
            },
          }
        }
      }

      // 推送结束消息，标识处理完成
      await sseChannel.end({
        type: 'end',
        content: isIntentContentStarted ? intentContent : fullContent,
        intentType: intentType,
        totalChunks: chunkCount,
        timestamp: Date.now(),
      })

      console.log(`SSE 流式聊天完成，共推送${chunkCount}个数据块`)

      return {
        errCode: 0,
        errMsg: 'success',
        data: {
          type: 'stream_complete',
          content: isIntentContentStarted ? intentContent : fullContent,
          intentType: intentType,
          totalChunks: chunkCount,
        },
      }
    } catch (error) {
      console.log('SSE 流式聊天错误：', error)

      try {
        if (channel) {
          const sseChannel = uniCloud.deserializeSSEChannel(channel)
          await sseChannel.end({
            type: 'error',
            error: error.message || '调用 AI 流式接口失败',
            timestamp: Date.now(),
          })
        }
      } catch (channelError) {
        console.log('发送错误消息失败：', channelError)
      }

      return {
        errCode: 'API_ERROR',
        errMsg: error.message || '调用 AI 流式接口失败',
      }
    }
  },
}
