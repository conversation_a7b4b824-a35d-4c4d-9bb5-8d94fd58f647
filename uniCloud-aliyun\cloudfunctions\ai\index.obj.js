/**
 * 执行上下文管理模块
 *
 * 主要功能：
 * 1. 管理任务执行过程中的所有数据和状态信息
 * 2. 提供步骤结果的存储和检索功能
 * 3. 自动提取和分析上下文数据
 * 4. 支持智能项目匹配和关键词提取
 * 5. 维护执行过程的元数据信息
 *
 * 核心特性：
 * - 会话隔离：每个执行会话都有独立的上下文
 * - 智能提取：自动从步骤结果中提取有用信息
 * - 项目匹配：基于用户输入智能匹配目标项目
 * - 数据关联：建立步骤间的数据依赖关系
 *
 * 使用场景：
 * - 多步骤任务执行时的数据传递
 * - 动态参数解析时的数据源
 * - 执行结果的统计和分析
 * - 用户意图的上下文理解
 *
 * <AUTHOR> 开发团队
 * @version 1.3.0
 * @since 2024-01-01
 */

/**
 * 执行上下文管理器
 * 用于管理任务执行过程中的所有数据和状态信息
 *
 * 数据结构：
 * - stepResults: Map<stepId, {result, metadata}> 存储步骤执行结果
 * - contextData: Map<key, value> 存储提取的上下文数据
 * - metadata: Object 存储执行过程的元数据信息
 *
 * 核心功能：
 * - 步骤结果管理：存储和检索每个步骤的执行结果
 * - 上下文数据提取：自动从结果中提取关键信息
 * - 项目智能匹配：基于用户输入匹配最相关的项目
 * - 关键词提取：从用户输入中提取项目相关关键词
 */
class ExecutionContextManager {
  /**
   * 构造函数 - 初始化执行上下文管理器
   *
   * @param {string} sessionId - 会话唯一标识，用于区分不同的执行会话
   * @param {string} userInput - 用户输入的原始消息，用于上下文分析和项目匹配
   */
  constructor(sessionId, userInput) {
    this.sessionId = sessionId // 会话 ID，确保不同会话的数据隔离
    this.userInput = userInput // 用户原始输入，用于智能分析和关键词提取
    this.stepResults = new Map() // 存储各步骤的执行结果，key 为 stepId
    this.contextData = new Map() // 存储提取的上下文数据，key 为数据类型
    this.metadata = {
      startTime: Date.now(), // 执行开始时间戳
      currentStep: 0, // 当前执行步骤索引
      totalSteps: 0, // 总步骤数（在执行计划确定后设置）
    }
  }

  /**
   * 设置步骤执行结果
   * 存储指定步骤的执行结果，并自动提取上下文数据
   *
   * @param {string} stepId - 步骤唯一标识，用于后续检索
   * @param {Object} result - 步骤执行结果，包含工具调用的返回数据
   * @param {Object} metadata - 附加元数据，可选参数
   *
   * 功能特性：
   * - 自动添加时间戳和步骤索引
   * - 触发上下文数据的自动提取
   * - 支持元数据的扩展存储
   */
  setStepResult(stepId, result, metadata = {}) {
    this.stepResults.set(stepId, {
      result, // 原始执行结果
      metadata: {
        ...metadata, // 用户提供的元数据
        timestamp: Date.now(), // 记录结果存储时间
        stepIndex: this.metadata.currentStep, // 记录当前步骤索引
      },
    })
    // 自动从结果中提取有用的上下文数据
    // 这是智能数据提取的关键步骤
    this.extractContextData(stepId, result)
  }

  /**
   * 获取步骤执行结果
   * 根据步骤 ID 检索之前存储的执行结果
   *
   * @param {string} stepId - 步骤唯一标识
   * @returns {Object|undefined} 步骤执行结果，不存在时返回 undefined
   *
   * 使用场景：
   * - 后续步骤需要引用前面步骤的结果
   * - 动态参数解析时获取数据源
   * - 执行结果的统计和分析
   */
  getStepResult(stepId) {
    return this.stepResults.get(stepId)?.result
  }

  /**
   * 设置上下文数据
   * 手动设置特定键的上下文数据
   *
   * @param {string} key - 数据键名
   * @param {any} value - 数据值
   *
   * 使用场景：
   * - 手动添加特定的上下文信息
   * - 覆盖自动提取的数据
   * - 添加计算得出的衍生数据
   */
  setContextData(key, value) {
    this.contextData.set(key, value)
  }

  /**
   * 获取上下文数据
   * 根据键名获取提取的上下文数据
   *
   * @param {string} key - 数据键名，如 'projects'、'tasks' 等
   * @returns {any} 数据值，不存在时返回 undefined
   *
   * 常用键名：
   * - 'projects': 项目列表数据
   * - 'tasks': 任务列表数据
   * - 'completedTasks': 已完成任务列表
   * - 'uncompletedTasks': 未完成任务列表
   */
  getContextData(key) {
    return this.contextData.get(key)
  }

  /**
   * 从步骤结果中提取有用的上下文数据
   * 自动识别项目信息、任务统计等关键数据
   * @param {string} stepId - 步骤标识（用于日志记录）
   * @param {Object} result - 步骤执行结果
   */
  extractContextData(stepId, result) {
    // 处理项目列表数据
    if (result.data && Array.isArray(result.data)) {
      const targetProject = this.findTargetProject(result.data)
      if (targetProject) {
        this.setContextData('targetProject', targetProject)
        console.log(`从步骤 ${stepId} 提取到目标项目:`, targetProject.name)
      }
    }

    // 处理任务列表数据
    if (result.tasks && Array.isArray(result.tasks)) {
      this.setContextData('taskCount', result.tasks.length)
      this.setContextData(
        'uncompletedTasks',
        result.tasks.filter((t) => !t.completed)
      )
      console.log(`从步骤 ${stepId} 提取到任务统计：总数=${result.tasks.length}`)
    }
  }

  /**
   * 查找目标项目
   * 基于用户输入和项目列表，智能匹配最相关的项目
   *
   * 匹配算法：
   * 1. 从用户输入中提取项目相关关键词
   * 2. 计算每个项目与关键词的匹配分数
   * 3. 返回分数最高且大于 0 的项目
   *
   * @param {Array} projects - 项目列表，包含项目名称、描述等信息
   * @returns {Object|null} 匹配的项目对象，未匹配到时返回 null
   *
   * 使用场景：
   * - 用户创建任务时自动选择目标项目
   * - 查询任务时确定搜索范围
   * - 提供智能的项目推荐
   */
  findTargetProject(projects) {
    const userInput = this.userInput.toLowerCase()
    const keywords = this.extractKeywords(userInput)

    // 为每个项目计算匹配分数
    const scored = projects.map((project) => ({
      project,
      score: this.calculateMatchScore(project, keywords),
    }))

    // 按分数降序排序，选择最高分的项目
    scored.sort((a, b) => b.score - a.score)
    return scored.length > 0 && scored[0].score > 0 ? scored[0].project : null
  }

  /**
   * 提取关键词
   * 从用户输入中提取项目相关的关键词
   *
   * 提取规则：
   * - 匹配 "XXX 项目" 或 "XXXproject" 格式
   * - 提取其中的项目名称部分
   * - 转换为小写便于匹配
   *
   * @param {string} input - 用户输入文本
   * @returns {Array<string>} 提取的关键词数组
   *
   * 示例：
   * - "帮我在学习项目中创建任务" → ["学习"]
   * - "查看工作 project 的进度" → ["工作"]
   */
  extractKeywords(input) {
    const keywords = []
    // 使用正则表达式匹配项目相关的表达式
    const projectMatches = input.match(/(\w+)项目|(\w+)project/gi)
    if (projectMatches) {
      projectMatches.forEach((match) => {
        // 移除 "项目" 或 "project" 后缀，提取核心关键词
        const keyword = match.replace(/项目|project/gi, '')
        if (keyword) keywords.push(keyword.toLowerCase())
      })
    }
    return keywords
  }

  /**
   * 计算匹配分数
   * 根据项目名称与关键词的匹配程度计算分数
   *
   * 计算规则：
   * - 每个关键词在项目名称中出现一次，分数 +1
   * - 支持部分匹配（包含关系）
   * - 分数越高表示匹配度越好
   *
   * @param {Object} project - 项目对象，包含 name 属性
   * @param {Array<string>} keywords - 关键词数组
   * @returns {number} 匹配分数，0 表示无匹配
   *
   * 优化空间：
   * - 可以考虑完全匹配给更高分数
   * - 可以加入项目描述的匹配
   * - 可以考虑关键词的权重差异
   */
  calculateMatchScore(project, keywords) {
    let score = 0
    const projectName = project.name.toLowerCase()
    keywords.forEach((keyword) => {
      if (projectName.includes(keyword)) {
        score += 1 // 每个匹配的关键词贡献 1 分
      }
    })
    return score
  }
}

module.exports = {
  ExecutionContextManager,
}
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: '消息内容不能为空',
      }
    }

    if (!channel) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: 'SSE Channel 不能为空',
      }
    }

    try {
      // 反序列化 SSE Channel，将前端传递的 channel 对象转换为可用的推送通道
      const sseChannel = uniCloud.deserializeSSEChannel(channel)

      // 初始化豆包 AI 客户端，使用配置文件中的参数
      const openai = new OpenAI(doubaoParams)

      // 构建对话消息数组：系统提示词 + 历史记录 + 当前用户消息
      // 这个数组将作为 AI 的上下文，影响 AI 的回复内容和风格
      const messages = [{ role: 'system', content: system }, ...history_records]
      if (message) messages.push({ role: 'user', content: message })

      // 推送开始消息，通知前端开始处理用户请求
      // 这有助于前端显示加载状态，提升用户体验
      await sseChannel.write({
        type: 'start',
        message: '开始生成回复...',
        timestamp: Date.now(),
      })

      // 创建流式 AI 响应，启用实时数据传输
      // 流式响应允许我们在 AI 生成内容的同时就开始处理和推送数据
      const streamResponse = await openai.chat.completions.create({
        messages, // 对话上下文
        model: model, // 使用的 AI 模型
        stream: true, // 启用流式响应，关键设置
        timeout: 300000, // 5 分钟超时（毫秒），防止长时间等待
      })

      // 初始化流式处理相关变量
      // 这些变量用于管理流式数据的接收、解析和推送过程
      let fullContent = '' // 累积的完整 AI 响应内容，用于最终的意图解析
      let chunkCount = 0 // 推送的数据块计数，用于统计和调试
      let intentType = null // 识别的意图类型：create_task|find_task|chat
      let isIntentContentStarted = false // 是否开始推送意图内容的标志位
      let intentContent = '' // 提取的意图内容，即 AI 解析后的具体任务内容

      // 正则表达式：匹配 AI 返回的意图类型和内容
      // 这些正则表达式用于实时解析 AI 的流式响应，提取结构化信息
      // 意图类型格式：「意图类型」：create_task|find_task|chat
      const intentTypeRegex = /「意图类型」：(create_task|find_task|chat)/
      // 意图内容格式：「意图内容」：具体内容...
      // 使用 [\s\S]* 匹配包括换行符在内的所有字符
      const intentContentRegex = /「意图内容」：([\s\S]*)/

      // 流式处理 AI 响应数据
      // 这是核心的数据处理循环，实时接收和解析 AI 的流式响应
      for await (const chunk of streamResponse) {
        // 提取当前数据块的内容，处理可能的空值情况
        const content = chunk.choices[0]?.delta?.content || ''

        if (content) {
          fullContent += content // 累积完整内容，用于后续的正则匹配
          chunkCount++ // 增加数据块计数，用于统计和调试

          // 第一阶段：检测意图类型
          // 在累积的内容中查找意图类型标识，一旦找到就立即处理
          if (!intentType) {
            const typeMatch = intentTypeRegex.exec(fullContent)
            if (typeMatch) {
              intentType = typeMatch[1] // 提取意图类型：create_task|find_task|chat
              console.log(`检测到意图类型：${intentType}`)

              // 立即推送意图类型到前端，让用户知道 AI 已经理解了请求类型
              await sseChannel.write({
                type: 'intent_type',
                intentType: intentType,
                timestamp: Date.now(),
              })
              continue // 跳过当前块的推送，避免重复发送相同信息
            }
          }

          // 第二阶段：检测意图内容开始
          // 在已识别意图类型的基础上，查找意图内容的开始标识
          if (intentType && !isIntentContentStarted) {
            const contentMatch = intentContentRegex.exec(fullContent)
            if (contentMatch) {
              isIntentContentStarted = true
              intentContent = contentMatch[1] // 提取已有的意图内容
              console.log('检测到意图内容开始')

              // 推送意图内容开始标识，通知前端开始接收具体的任务内容
              await sseChannel.write({
                type: 'intent_content_start',
                content: intentContent,
                timestamp: Date.now(),
              })
              continue // 跳过当前块的推送，避免重复发送
            }
          } else if (isIntentContentStarted) {
            // 第三阶段：持续推送意图内容块
            // 一旦开始推送意图内容，就将后续的每个数据块都发送给前端
            await sseChannel.write({
              type: 'intent_content_chunk',
              content: content,
              timestamp: Date.now(),
            })
            intentContent += content // 累积意图内容，用于最终的任务执行
            console.log(`推送意图内容块：${content}`)
          } else {
            // 尚未检测到完整的意图格式，继续累积内容
            // 这种情况通常发生在 AI 响应的开始阶段
            console.log(`累积内容：${fullContent}`)
          }
        }
      }

      // 调试输出：打印完整的处理结果，便于开发和调试
      console.log('AI 完整返回内容：', fullContent)
      console.log('提取的意图类型：', intentType)
      console.log('提取的意图内容：', intentContent)

      // 任务执行阶段：非聊天类型触发智能任务执行
      // 只有当识别到具体的任务意图（create_task 或 find_task）时才执行任务
      if (intentType && intentType !== 'chat') {
        // 创建执行上下文管理器，用于管理执行过程中的数据和状态
        // 传入唯一的会话 ID 和用户原始输入，便于上下文分析和数据关联
        const context = new ExecutionContextManager(IntelligentExecutionPlanner.generateUUID(), message)

        // 使用智能执行计划生成器创建执行计划
        // 基于用户输入和意图类型，AI 会生成具体的执行步骤
        const executionPlan = await IntelligentExecutionPlanner.generatePlan(message, intentType)

        // 如果生成了有效的执行计划，则开始执行任务
        if (executionPlan.totalSteps > 0) {
          // 使用增强的执行引擎执行计划
          // 集成了错误处理、重试机制、性能监控等高级功能
          await executeRobustPlan(executionPlan, context, sseChannel, globalPerformanceMonitor)

          // 返回任务执行结果，包含详细的执行信息和性能报告
          return {
            errCode: 0,
            errMsg: 'success',
            data: {
              type: 'task_executed', // 标识这是一个任务执行结果
              intentType: intentType, // 识别的意图类型
              executionPlan: executionPlan, // 完整的执行计划信息
              contextData: Array.from(context.contextData.keys()), // 上下文数据键列表
              executionTime: executionPlan.totalExecutionTime, // 总执行时间
              performanceReport: globalPerformanceMonitor.getPerformanceReport(), // 性能报告
              content: isIntentContentStarted ? intentContent : fullContent, // AI 回复内容
              totalChunks: chunkCount, // 推送的数据块总数
            },
          }
        }
      }

      // 推送结束消息，标识流式聊天处理完成
      // 这是正常流程的结束，通知前端可以停止等待更多数据
      await sseChannel.end({
        type: 'end',
        content: isIntentContentStarted ? intentContent : fullContent, // 返回解析后的内容或完整内容
        intentType: intentType, // 识别的意图类型
        totalChunks: chunkCount, // 总共推送的数据块数量
        timestamp: Date.now(),
      })

      // 调试日志：记录流式聊天的完成情况
      console.log(`SSE 流式聊天完成，共推送${chunkCount}个数据块`)

      // 返回流式聊天完成的结果
      return {
        errCode: 0,
        errMsg: 'success',
        data: {
          type: 'stream_complete', // 标识这是一个流式聊天完成结果
          content: isIntentContentStarted ? intentContent : fullContent,
          intentType: intentType,
          totalChunks: chunkCount,
        },
      }
    } catch (error) {
      // 错误处理：捕获并处理执行过程中的所有异常
      console.log('SSE 流式聊天错误：', error)

      // 尝试通过 SSE Channel 发送错误消息给前端
      // 这样前端可以知道发生了错误，而不是一直等待
      try {
        if (channel) {
          const sseChannel = uniCloud.deserializeSSEChannel(channel)
          await sseChannel.end({
            type: 'error',
            error: error.message || '调用 AI 流式接口失败',
            timestamp: Date.now(),
          })
        }
      } catch (channelError) {
        // 如果连错误消息都发送失败，记录日志但不抛出异常
        console.log('发送错误消息失败：', channelError)
      }

      // 返回错误结果给调用方
      return {
        errCode: 'API_ERROR',
        errMsg: error.message || '调用 AI 流式接口失败',
      }
    }
  },
}
