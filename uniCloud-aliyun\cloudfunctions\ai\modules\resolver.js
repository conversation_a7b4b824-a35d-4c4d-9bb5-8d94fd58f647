// 动态参数解析模块
// 负责解析执行步骤中的动态参数引用

// 动态参数解析器
class DynamicParameterResolver {
  static async resolveParameters(step, context) {
    const { parameters, dependencies } = step
    const resolved = { ...parameters }

    // 等待依赖步骤完成
    for (const depId of dependencies) {
      await this.waitForStepCompletion(depId, context)
    }

    // 解析动态参数
    for (const [key, value] of Object.entries(resolved)) {
      if (typeof value === 'string') {
        resolved[key] = await this.resolveDynamicValue(value, context)
      }
    }

    return resolved
  }

  static async resolveDynamicValue(value, context) {
    // 处理上下文引用：$context.key
    if (value.startsWith('$context.')) {
      const contextPath = value.substring(9)
      const [contextKey, ...pathParts] = contextPath.split('.')
      const contextValue = context.getContextData(contextKey)
      if (contextValue !== undefined) {
        // 如果有嵌套路径，继续提取
        if (pathParts.length > 0) {
          return this.extractValueByPath(contextValue, pathParts.join('.'))
        }
        return contextValue
      }
      throw new Error(`上下文数据不存在：${contextKey}`)
    }

    // 处理步骤结果引用：$step.stepId.path
    if (value.startsWith('$step.')) {
      const [, stepId, ...pathParts] = value.split('.')
      const stepResult = context.getStepResult(stepId)

      if (!stepResult) {
        throw new Error(`步骤结果不存在：${stepId}`)
      }

      return this.extractValueByPath(stepResult, pathParts.join('.'))
    }

    // 处理筛选表达式：$filter(stepId.path, condition)
    if (value.startsWith('$filter(')) {
      return this.processFilterExpression(value, context)
    }

    return value
  }

  static extractValueByPath(obj, path) {
    if (!path) return obj

    return path.split('.').reduce((current, key) => {
      // 处理数组索引：projects[0]
      const arrayMatch = key.match(/^(\w+)\[(\d+)\]$/)
      if (arrayMatch) {
        const [, arrayKey, index] = arrayMatch
        return current?.[arrayKey]?.[parseInt(index)]
      }

      // 处理数组筛选：projects[name=okr]
      const filterMatch = key.match(/^(\w+)\[(\w+)=(.+)\]$/)
      if (filterMatch) {
        const [, arrayKey, filterKey, filterValue] = filterMatch
        const array = current?.[arrayKey]
        if (Array.isArray(array)) {
          return array.find((item) => item[filterKey]?.toLowerCase().includes(filterValue.toLowerCase()))
        }
      }

      return current?.[key]
    }, obj)
  }

  static async processFilterExpression(expression, context) {
    // 解析筛选表达式：$filter(step1.data, name contains "okr")
    const match = expression.match(/\$filter\(([^,]+),\s*(.+)\)/)
    if (!match) {
      throw new Error(`无效的筛选表达式：${expression}`)
    }

    const [, dataPath, condition] = match

    // 处理步骤结果引用
    if (dataPath.startsWith('step')) {
      const [stepId, ...pathParts] = dataPath.split('.')
      const stepResult = context.getStepResult(stepId)

      if (!stepResult) {
        throw new Error(`步骤结果不存在：${stepId}`)
      }

      const data = this.extractValueByPath(stepResult, pathParts.join('.'))

      if (!Array.isArray(data)) {
        throw new Error(`筛选目标必须是数组：${dataPath}`)
      }

      return this.applyFilter(data, condition)
    } else {
      // 其他类型的数据路径
      const data = await this.resolveDynamicValue(`$${dataPath}`, context)

      if (!Array.isArray(data)) {
        throw new Error(`筛选目标必须是数组：${dataPath}`)
      }

      return this.applyFilter(data, condition)
    }
  }

  static applyFilter(array, condition) {
    // 解析条件：name contains "okr"
    const conditionMatch = condition.match(/(\w+)\s+(contains|equals|startsWith)\s+"([^"]+)"/)
    if (!conditionMatch) {
      throw new Error(`无效的筛选条件：${condition}`)
    }

    const [, field, operator, value] = conditionMatch

    return array.filter((item) => {
      const fieldValue = item[field]?.toString().toLowerCase() || ''
      const searchValue = value.toLowerCase()

      switch (operator) {
        case 'contains':
          return fieldValue.includes(searchValue)
        case 'equals':
          return fieldValue === searchValue
        case 'startsWith':
          return fieldValue.startsWith(searchValue)
        default:
          return false
      }
    })
  }

  static async waitForStepCompletion(stepId, context) {
    // 等待依赖步骤完成的逻辑
    return new Promise((resolve) => {
      const checkCompletion = () => {
        const stepResult = context.getStepResult(stepId)
        if (stepResult !== undefined) {
          resolve()
        } else {
          setTimeout(checkCompletion, 100)
        }
      }
      checkCompletion()
    })
  }
}

module.exports = {
  DynamicParameterResolver,
}
