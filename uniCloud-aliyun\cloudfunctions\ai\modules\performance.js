// 性能监控模块
// 负责监控和记录系统性能指标
// 提供全面的性能数据收集、分析和报告功能

/**
 * 性能监控器
 * 用于收集和分析系统运行时的各项性能指标
 * 支持执行时间统计、成功率监控、错误分析等功能
 */
class PerformanceMonitor {
  constructor() {
    // 初始化性能指标数据结构
    this.metrics = {
      // 执行计划相关指标
      planGenerationTime: [], // 计划生成耗时记录
      planExecutionTime: [], // 计划执行耗时记录
      averageStepTime: [], // 平均步骤耗时记录

      // 工具调用相关指标
      toolCallSuccessRate: 0, // 工具调用成功率
      toolCallCount: 0, // 工具调用总次数
      toolCallFailures: 0, // 工具调用失败次数
      averageToolResponseTime: [], // 工具响应时间记录
      retryRate: 0, // 重试率

      // 参数解析相关指标
      parameterResolutionTime: [], // 参数解析耗时记录
      dynamicReferenceCount: 0, // 动态引用总数
      resolutionSuccessRate: 0, // 解析成功率

      // 错误统计
      errorsByType: {}, // 按错误类型分类统计
      errorsByTool: {}, // 按工具分类错误统计

      // 用户体验指标
      userSatisfactionScore: 0, // 用户满意度评分
      taskCompletionRate: 0, // 任务完成率
    }
  }

  /**
   * 记录执行计划生成时间
   * @param {number} startTime - 开始时间戳
   * @param {number} endTime - 结束时间戳
   */
  recordPlanGeneration(startTime, endTime) {
    const duration = endTime - startTime
    this.metrics.planGenerationTime.push(duration)

    // 保持最近 100 条记录，避免内存占用过多
    if (this.metrics.planGenerationTime.length > 100) {
      this.metrics.planGenerationTime.shift()
    }
  }

  /**
   * 记录工具调用结果
   * @param {string} toolName - 工具名称
   * @param {boolean} success - 是否成功
   * @param {number} responseTime - 响应时间（毫秒）
   * @param {number} retryCount - 重试次数
   */
  recordToolCall(toolName, success, responseTime, retryCount = 0) {
    this.metrics.toolCallCount++

    if (success) {
      // 记录成功调用的响应时间
      this.metrics.averageToolResponseTime.push(responseTime)
    } else {
      // 记录失败统计
      this.metrics.toolCallFailures++
      this.metrics.errorsByTool[toolName] = (this.metrics.errorsByTool[toolName] || 0) + 1
    }

    // 更新重试率
    if (retryCount > 0) {
      this.metrics.retryRate =
        (this.metrics.retryRate * (this.metrics.toolCallCount - 1) + 1) / this.metrics.toolCallCount
    }

    // 实时更新成功率
    this.metrics.toolCallSuccessRate =
      (this.metrics.toolCallCount - this.metrics.toolCallFailures) / this.metrics.toolCallCount
  }

  // 记录错误
  recordError(errorType, errorMessage, context = {}) {
    this.metrics.errorsByType[errorType] = (this.metrics.errorsByType[errorType] || 0) + 1

    // 记录详细错误信息用于分析
    console.log('Performance Monitor - Error Recorded:', {
      type: errorType,
      message: errorMessage,
      context: context,
      timestamp: Date.now(),
    })
  }

  // 记录参数解析性能
  recordParameterResolution(startTime, endTime, dynamicRefCount, success) {
    const duration = endTime - startTime
    this.metrics.parameterResolutionTime.push(duration)
    this.metrics.dynamicReferenceCount += dynamicRefCount

    // 更新解析成功率
    const totalResolutions = this.metrics.parameterResolutionTime.length
    const currentSuccessRate = this.metrics.resolutionSuccessRate
    this.metrics.resolutionSuccessRate =
      (currentSuccessRate * (totalResolutions - 1) + (success ? 1 : 0)) / totalResolutions
  }

  // 获取性能报告
  getPerformanceReport() {
    return {
      summary: {
        totalToolCalls: this.metrics.toolCallCount,
        successRate: this.metrics.toolCallSuccessRate,
        averageResponseTime: this.calculateAverage(this.metrics.averageToolResponseTime),
        retryRate: this.metrics.retryRate,
      },
      planGeneration: {
        averageTime: this.calculateAverage(this.metrics.planGenerationTime),
        samples: this.metrics.planGenerationTime.length,
      },
      parameterResolution: {
        averageTime: this.calculateAverage(this.metrics.parameterResolutionTime),
        successRate: this.metrics.resolutionSuccessRate,
        totalDynamicReferences: this.metrics.dynamicReferenceCount,
      },
      errors: {
        byType: this.metrics.errorsByType,
        byTool: this.metrics.errorsByTool,
      },
      timestamp: Date.now(),
    }
  }

  calculateAverage(array) {
    if (array.length === 0) return 0
    return array.reduce((sum, val) => sum + val, 0) / array.length
  }

  // 重置指标
  reset() {
    this.metrics = {
      planGenerationTime: [],
      planExecutionTime: [],
      averageStepTime: [],
      toolCallSuccessRate: 0,
      toolCallCount: 0,
      toolCallFailures: 0,
      averageToolResponseTime: [],
      retryRate: 0,
      parameterResolutionTime: [],
      dynamicReferenceCount: 0,
      resolutionSuccessRate: 0,
      errorsByType: {},
      errorsByTool: {},
      userSatisfactionScore: 0,
      taskCompletionRate: 0,
    }
  }
}

/**
 * 全局性能监控实例
 * 整个应用共享的性能监控器，用于收集所有执行过程的性能数据
 * 在 executeRobustPlan 函数中被默认使用
 *
 * 使用方式：
 * - 在执行引擎中自动记录性能数据
 * - 通过 getPerformanceReport() 获取性能报告
 * - 可以通过 reset() 重置统计数据
 */
const globalPerformanceMonitor = new PerformanceMonitor()

module.exports = {
  PerformanceMonitor, // 性能监控器类，可创建新实例
  globalPerformanceMonitor, // 全局共享实例
}
