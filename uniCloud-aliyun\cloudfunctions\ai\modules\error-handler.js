// 错误处理模块
// 负责处理执行过程中的各种错误和异常情况

// 分层错误处理器
class EnhancedErrorHandler {
  static async handleToolError(error, step, context, sseChannel) {
    const errorInfo = {
      stepId: step.stepId,
      toolName: step.toolName,
      error: error.message,
      timestamp: Date.now(),
      retryCount: step.retryCount,
    }

    // 1. 参数验证错误（不可重试）
    if (error.message.includes('参数验证失败') || error.message.includes('缺少必需参数')) {
      await sseChannel.write({
        type: 'step_error',
        ...errorInfo,
        errorType: 'validation_error',
        recoverable: false,
        suggestions: this.generateValidationSuggestions(error),
      })
      throw error
    }

    // 2. 工具执行错误（可重试）
    if (step.retryCount < step.maxRetries) {
      await this.retryStep(step, context, sseChannel)
      return 'retry'
    }

    // 3. 降级处理
    const fallbackResult = await this.attemptFallback(step, context)
    if (fallbackResult) {
      await sseChannel.write({
        type: 'step_fallback',
        stepId: step.stepId,
        fallbackResult,
        originalError: error.message,
        timestamp: Date.now(),
      })
      return fallbackResult
    }

    // 4. 最终失败
    await sseChannel.write({
      type: 'step_error',
      ...errorInfo,
      errorType: 'execution_error',
      recoverable: false,
      suggestions: this.generateErrorSuggestions(error, step),
    })

    throw new Error(`工具执行失败：${error.message}`)
  }

  static async retryStep(step, context, sseChannel) {
    step.retryCount++

    await sseChannel.write({
      type: 'step_retry',
      stepId: step.stepId,
      retryCount: step.retryCount,
      maxRetries: step.maxRetries,
      timestamp: Date.now(),
    })

    // 指数退避重试
    const delay = Math.min(1000 * Math.pow(2, step.retryCount - 1), 10000)
    await new Promise((resolve) => setTimeout(resolve, delay))
  }

  static async attemptFallback(step, context) {
    // 根据工具类型尝试降级方案
    switch (step.toolName) {
      case 'getProjects':
        // 如果获取项目列表失败，尝试使用缓存或默认项目
        return this.getFallbackProjects(context)

      case 'getTasks':
        // 如果获取任务失败，返回空列表但保持流程继续
        return {
          success: true,
          data: [],
          metadata: {
            fallback: true,
            message: '暂时无法获取任务列表，请稍后重试',
          },
        }

      default:
        return null
    }
  }

  static generateValidationSuggestions(error) {
    const suggestions = []

    if (error.message.includes('缺少必需参数')) {
      suggestions.push('请检查输入是否包含必要的信息')
    }

    if (error.message.includes('格式不符合要求')) {
      suggestions.push('请使用正确的格式输入')
    }

    return suggestions
  }

  static generateErrorSuggestions(error, step) {
    const suggestions = []

    if (error.message.includes('API rate limit')) {
      suggestions.push('请稍后重试，API 调用频率过高')
    }

    if (error.message.includes('unauthorized')) {
      suggestions.push('请检查 API 密钥配置')
    }

    if (step.toolName === 'getProjects' && error.message.includes('not found')) {
      suggestions.push('请确认您有访问项目列表的权限')
    }

    return suggestions
  }

  static async getFallbackProjects(context) {
    // 返回默认项目或缓存项目
    return {
      success: true,
      data: [{ id: 'default', name: '默认项目', description: '系统默认项目' }],
      metadata: {
        fallback: true,
        source: 'default',
        message: '使用默认项目，请稍后重试获取完整项目列表',
      },
    }
  }
}

module.exports = {
  EnhancedErrorHandler,
}
