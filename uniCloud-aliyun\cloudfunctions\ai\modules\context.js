// 执行上下文管理模块
// 负责管理执行过程中的上下文数据和步骤结果
// 提供数据存储、检索和智能提取功能

/**
 * 执行上下文管理器
 * 用于管理任务执行过程中的所有数据和状态信息
 * 支持步骤结果存储、上下文数据提取、项目匹配等功能
 */
class ExecutionContextManager {
  /**
   * 构造函数
   * @param {string} sessionId - 会话唯一标识
   * @param {string} userInput - 用户输入的原始消息
   */
  constructor(sessionId, userInput) {
    this.sessionId = sessionId // 会话 ID，用于区分不同的执行会话
    this.userInput = userInput // 用户原始输入，用于上下文分析
    this.stepResults = new Map() // 存储各步骤的执行结果
    this.contextData = new Map() // 存储提取的上下文数据
    this.metadata = {
      startTime: Date.now(), // 执行开始时间
      currentStep: 0, // 当前执行步骤索引
      totalSteps: 0, // 总步骤数
    }
  }

  /**
   * 设置步骤执行结果
   * @param {string} stepId - 步骤唯一标识
   * @param {Object} result - 步骤执行结果
   * @param {Object} metadata - 附加元数据
   */
  setStepResult(stepId, result, metadata = {}) {
    this.stepResults.set(stepId, {
      result,
      metadata: {
        ...metadata,
        timestamp: Date.now(), // 记录结果存储时间
        stepIndex: this.metadata.currentStep, // 记录步骤索引
      },
    })
    // 自动从结果中提取有用的上下文数据
    this.extractContextData(stepId, result)
  }

  /**
   * 获取步骤执行结果
   * @param {string} stepId - 步骤唯一标识
   * @returns {Object|undefined} 步骤执行结果
   */
  getStepResult(stepId) {
    return this.stepResults.get(stepId)?.result
  }

  /**
   * 设置上下文数据
   * @param {string} key - 数据键名
   * @param {any} value - 数据值
   */
  setContextData(key, value) {
    this.contextData.set(key, value)
  }

  /**
   * 获取上下文数据
   * @param {string} key - 数据键名
   * @returns {any} 数据值
   */
  getContextData(key) {
    return this.contextData.get(key)
  }

  /**
   * 从步骤结果中提取有用的上下文数据
   * 自动识别项目信息、任务统计等关键数据
   * @param {string} stepId - 步骤标识（用于日志记录）
   * @param {Object} result - 步骤执行结果
   */
  extractContextData(stepId, result) {
    // 处理项目列表数据
    if (result.data && Array.isArray(result.data)) {
      const targetProject = this.findTargetProject(result.data)
      if (targetProject) {
        this.setContextData('targetProject', targetProject)
        console.log(`从步骤 ${stepId} 提取到目标项目:`, targetProject.name)
      }
    }

    // 处理任务列表数据
    if (result.tasks && Array.isArray(result.tasks)) {
      this.setContextData('taskCount', result.tasks.length)
      this.setContextData(
        'uncompletedTasks',
        result.tasks.filter((t) => !t.completed)
      )
      console.log(`从步骤 ${stepId} 提取到任务统计: 总数=${result.tasks.length}`)
    }
  }

  findTargetProject(projects) {
    const userInput = this.userInput.toLowerCase()
    const keywords = this.extractKeywords(userInput)

    const scored = projects.map((project) => ({
      project,
      score: this.calculateMatchScore(project, keywords),
    }))

    scored.sort((a, b) => b.score - a.score)
    return scored.length > 0 && scored[0].score > 0 ? scored[0].project : null
  }

  extractKeywords(input) {
    const keywords = []
    const projectMatches = input.match(/(\w+)项目|(\w+)project/gi)
    if (projectMatches) {
      projectMatches.forEach((match) => {
        const keyword = match.replace(/项目|project/gi, '')
        if (keyword) keywords.push(keyword.toLowerCase())
      })
    }
    return keywords
  }

  calculateMatchScore(project, keywords) {
    let score = 0
    const projectName = project.name.toLowerCase()
    keywords.forEach((keyword) => {
      if (projectName.includes(keyword)) {
        score += 1
      }
    })
    return score
  }
}

module.exports = {
  ExecutionContextManager,
}
