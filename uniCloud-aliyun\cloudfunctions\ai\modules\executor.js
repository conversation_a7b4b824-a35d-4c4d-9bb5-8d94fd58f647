/**
 * 执行引擎模块
 *
 * 主要功能：
 * 1. 负责执行计划的实际执行，包括简单执行、智能执行和增强执行
 * 2. 提供三个版本的执行引擎，支持不同复杂度的任务执行需求
 * 3. 集成参数验证、动态解析、错误处理和性能监控
 * 4. 提供实时的执行状态推送和进度跟踪
 *
 * 执行引擎版本：
 * - V1.1 基础工具调用引擎：简单的顺序执行，适用于基础功能测试
 * - V1.2 智能执行引擎：支持动态参数解析和智能错误处理
 * - V1.3 增强执行引擎：集成性能监控和高级错误恢复机制
 *
 * 核心特性：
 * - 多版本支持：根据任务复杂度选择合适的执行引擎
 * - 动态参数解析：支持步骤间的数据传递和上下文引用
 * - 智能错误处理：多层次的错误处理和自动恢复机制
 * - 性能监控：实时监控执行性能和资源使用情况
 * - 实时推送：通过 SSE 推送详细的执行状态和结果
 *
 * 技术架构：
 * - 模块化设计：每个执行引擎独立实现，便于维护和扩展
 * - 依赖注入：通过构造函数注入各种服务和工具
 * - 异步执行：全面支持异步操作和并发控制
 * - 状态管理：完整的执行状态跟踪和管理
 *
 * <AUTHOR> 开发团队
 * @version 1.3.0
 * @since 2024-01-01
 */

const { ParameterValidator } = require('./validator')
const { DynamicParameterResolver } = require('./resolver')
const { EnhancedErrorHandler } = require('./error-handler')
const {
  callRealTool,
  simulateToolCall,
  getContextUpdates,
  generateExecutionSummary,
  countDynamicReferences,
} = require('./tools')
const { globalPerformanceMonitor } = require('./performance')

/**
 * V1.1 基础工具调用引擎
 * 简单的顺序执行引擎，适用于基础功能测试和简单任务执行
 *
 * 特点：
 * - 顺序执行：严格按照步骤顺序执行，不支持并行处理
 * - 基础功能：不支持动态参数解析和复杂错误处理
 * - 快速失败：遇到错误立即停止执行，不进行重试
 * - 轻量级：最小的资源占用和处理开销
 *
 * 适用场景：
 * - 开发和测试阶段的快速验证
 * - 简单的单步骤或少步骤任务
 * - 基础功能的原型验证
 * - 调试和问题排查
 *
 * 限制：
 * - 不支持动态参数解析
 * - 不支持步骤间的数据依赖
 * - 错误处理能力有限
 * - 无性能监控功能
 *
 * @param {Object} executionPlan - 执行计划对象，包含步骤定义和元数据
 * @param {Object} context - 执行上下文管理器，用于数据存储和检索
 * @param {Object} sseChannel - SSE 推送通道，用于实时状态推送
 * @returns {Object} 执行结果对象，包含成功状态和执行信息
 */
async function executeSimplePlan(executionPlan, context, sseChannel) {
  try {
    // 推送执行计划开始消息
    // 通知前端开始执行计划，提供计划基本信息
    await sseChannel.write({
      type: 'execution_plan_start',
      plan: {
        planId: executionPlan.planId, // 计划唯一标识
        totalSteps: executionPlan.totalSteps, // 总步骤数，用于进度计算
      },
      timestamp: Date.now(),
    })

    // 更新计划状态为执行中
    executionPlan.status = 'executing'

    // 遍历执行计划中的所有步骤，按顺序执行
    for (let i = 0; i < executionPlan.steps.length; i++) {
      const step = executionPlan.steps[i]
      // 更新上下文中的当前步骤索引
      context.metadata.currentStep = i

      // 推送当前执行步骤信息
      // 让前端知道正在执行哪个步骤，显示进度和状态
      await sseChannel.write({
        type: 'execution_step',
        step: {
          stepId: step.stepId, // 步骤唯一标识
          description: step.description, // 步骤描述
          toolName: step.toolName, // 要调用的工具名称
        },
        timestamp: Date.now(),
      })

      // 更新步骤状态为执行中
      step.status = 'executing'

      try {
        // V1.1 版本：简单的工具调用（不支持动态参数）
        const validatedParams = ParameterValidator.validate(step.toolName, step.parameters)

        // 模拟工具调用（V1.1 版本先返回模拟数据）
        const result = await simulateToolCall(step.toolName, validatedParams)

        // 存储结果到上下文
        context.setStepResult(step.stepId, result)

        step.status = 'completed'

        // 推送步骤执行结果
        await sseChannel.write({
          type: 'step_result',
          stepId: step.stepId,
          result: result,
          timestamp: Date.now(),
        })
      } catch (error) {
        step.status = 'failed'
        step.error = error.message

        await sseChannel.write({
          type: 'step_error',
          stepId: step.stepId,
          error: error.message,
          timestamp: Date.now(),
        })

        throw error
      }
    }

    executionPlan.status = 'completed'

    // 推送执行完成
    await sseChannel.write({
      type: 'execution_complete',
      plan: executionPlan,
      contextData: Array.from(context.contextData.keys()),
      timestamp: Date.now(),
    })

    return executionPlan
  } catch (error) {
    executionPlan.status = 'failed'
    executionPlan.error = error.message

    await sseChannel.write({
      type: 'execution_failed',
      plan: executionPlan,
      error: error.message,
      timestamp: Date.now(),
    })

    throw error
  }
}

// V1.2 智能执行引擎
async function executeIntelligentPlan(executionPlan, context, sseChannel) {
  try {
    // 推送执行计划
    await sseChannel.write({
      type: 'execution_plan',
      plan: {
        planId: executionPlan.planId,
        totalSteps: executionPlan.totalSteps,
        estimatedTotalTime: executionPlan.estimatedTotalTime,
      },
      timestamp: Date.now(),
    })

    executionPlan.status = 'executing'

    for (let i = 0; i < executionPlan.steps.length; i++) {
      const step = executionPlan.steps[i]
      context.metadata.currentStep = i

      // 推送当前执行步骤
      await sseChannel.write({
        type: 'execution_step',
        step: {
          stepId: step.stepId,
          description: step.description,
          toolName: step.toolName,
          estimatedTime: step.estimatedTime,
        },
        timestamp: Date.now(),
      })

      step.status = 'executing'
      const stepStartTime = Date.now()

      try {
        // V1.2核心：动态参数解析
        const resolvedParams = await DynamicParameterResolver.resolveParameters(step, context)

        // 参数验证
        const validatedParams = ParameterValidator.validate(step.toolName, resolvedParams)

        // 真实工具调用
        const result = await callRealTool(step.toolName, validatedParams)

        // 存储结果到上下文
        context.setStepResult(step.stepId, result)

        step.status = 'completed'
        step.executionTime = Date.now() - stepStartTime

        // 推送步骤执行结果
        await sseChannel.write({
          type: 'step_result',
          stepId: step.stepId,
          result: result,
          executionTime: step.executionTime,
          contextUpdates: getContextUpdates(context),
          timestamp: Date.now(),
        })
      } catch (error) {
        step.executionTime = Date.now() - stepStartTime
        step.status = 'failed'
        step.error = error.message

        await sseChannel.write({
          type: 'step_error',
          stepId: step.stepId,
          error: error.message,
          executionTime: step.executionTime,
          timestamp: Date.now(),
        })

        // V1.2版本：简单的错误处理，V1.3版本会完善
        throw error
      }
    }

    executionPlan.status = 'completed'
    executionPlan.endTime = Date.now()
    executionPlan.totalExecutionTime = executionPlan.endTime - executionPlan.startTime

    // 推送执行完成
    await sseChannel.write({
      type: 'execution_complete',
      plan: executionPlan,
      summary: generateExecutionSummary(executionPlan, context),
      timestamp: Date.now(),
    })

    return executionPlan
  } catch (error) {
    executionPlan.status = 'failed'
    executionPlan.error = error.message

    await sseChannel.write({
      type: 'execution_failed',
      plan: executionPlan,
      error: error.message,
      timestamp: Date.now(),
    })

    throw error
  }
}

/**
 * V1.3 增强的执行引擎
 * 集成错误处理和性能监控的完整执行引擎
 * 支持动态参数解析、智能重试、降级处理和性能统计
 *
 * @param {Object} executionPlan - 执行计划对象
 * @param {Object} context - 执行上下文管理器
 * @param {Object} sseChannel - SSE 推送通道
 * @param {Object} performanceMonitor - 性能监控器实例
 * @returns {Object} 执行结果
 */
async function executeRobustPlan(executionPlan, context, sseChannel, performanceMonitor = globalPerformanceMonitor) {
  const planStartTime = Date.now() // 记录计划开始时间

  try {
    // 推送执行计划信息到前端
    await sseChannel.write({
      type: 'execution_plan',
      plan: {
        planId: executionPlan.planId, // 计划唯一标识
        totalSteps: executionPlan.totalSteps, // 总步骤数
        estimatedTotalTime: executionPlan.estimatedTotalTime, // 预估总耗时
      },
      timestamp: Date.now(),
    })

    executionPlan.status = 'executing'

    for (let i = 0; i < executionPlan.steps.length; i++) {
      const step = executionPlan.steps[i]
      context.metadata.currentStep = i

      // 推送当前执行步骤
      await sseChannel.write({
        type: 'execution_step',
        step: {
          stepId: step.stepId,
          description: step.description,
          toolName: step.toolName,
          estimatedTime: step.estimatedTime,
        },
        timestamp: Date.now(),
      })

      step.status = 'executing'
      const stepStartTime = Date.now()
      let stepResult = null
      let stepSuccess = false

      // V1.3 增强：带重试的步骤执行
      while (step.retryCount <= step.maxRetries) {
        try {
          // 参数解析性能监控
          const paramStartTime = Date.now()
          const resolvedParams = await DynamicParameterResolver.resolveParameters(step, context)
          const paramEndTime = Date.now()

          // 计算动态引用数量
          const dynamicRefCount = countDynamicReferences(step.parameters)
          performanceMonitor.recordParameterResolution(paramStartTime, paramEndTime, dynamicRefCount, true)

          // 参数验证
          const validatedParams = ParameterValidator.validate(step.toolName, resolvedParams)

          // 真实工具调用
          const toolCallStartTime = Date.now()
          stepResult = await callRealTool(step.toolName, validatedParams)
          const toolCallEndTime = Date.now()

          // 记录工具调用性能
          performanceMonitor.recordToolCall(step.toolName, true, toolCallEndTime - toolCallStartTime, step.retryCount)

          // 存储结果到上下文
          context.setStepResult(step.stepId, stepResult)

          step.status = 'completed'
          step.executionTime = Date.now() - stepStartTime
          stepSuccess = true

          // 推送步骤执行结果
          await sseChannel.write({
            type: 'step_result',
            stepId: step.stepId,
            result: stepResult,
            executionTime: step.executionTime,
            contextUpdates: getContextUpdates(context),
            retryCount: step.retryCount,
            timestamp: Date.now(),
          })

          break // 成功执行，跳出重试循环
        } catch (error) {
          step.executionTime = Date.now() - stepStartTime

          // 记录错误和性能
          performanceMonitor.recordError('step_execution', error.message, {
            stepId: step.stepId,
            toolName: step.toolName,
            retryCount: step.retryCount,
          })

          // V1.3 核心：增强的错误处理
          const errorHandleResult = await EnhancedErrorHandler.handleToolError(error, step, context, sseChannel)

          if (errorHandleResult === 'retry') {
            // 继续重试循环
            continue
          } else if (errorHandleResult && typeof errorHandleResult === 'object') {
            // 使用降级结果
            stepResult = errorHandleResult
            context.setStepResult(step.stepId, stepResult)
            step.status = 'completed_with_fallback'
            stepSuccess = true
            break
          } else {
            // 最终失败
            step.status = 'failed'
            step.error = error.message
            throw error
          }
        }
      }

      if (!stepSuccess) {
        throw new Error(`步骤 ${step.stepId} 执行失败，已达到最大重试次数`)
      }
    }

    executionPlan.status = 'completed'
    executionPlan.endTime = Date.now()
    executionPlan.totalExecutionTime = executionPlan.endTime - executionPlan.startTime

    // 记录计划执行性能
    performanceMonitor.recordPlanGeneration(planStartTime, executionPlan.endTime)

    // 推送执行完成
    await sseChannel.write({
      type: 'execution_complete',
      plan: executionPlan,
      summary: generateExecutionSummary(executionPlan, context),
      performanceReport: performanceMonitor.getPerformanceReport(),
      timestamp: Date.now(),
    })

    return executionPlan
  } catch (error) {
    executionPlan.status = 'failed'
    executionPlan.error = error.message
    executionPlan.endTime = Date.now()
    executionPlan.totalExecutionTime = executionPlan.endTime - planStartTime

    // 记录失败的计划执行
    performanceMonitor.recordError('plan_execution', error.message, {
      planId: executionPlan.planId,
      totalSteps: executionPlan.totalSteps,
    })

    await sseChannel.write({
      type: 'execution_failed',
      plan: executionPlan,
      error: error.message,
      performanceReport: performanceMonitor.getPerformanceReport(),
      timestamp: Date.now(),
    })

    throw error
  }
}

module.exports = {
  executeSimplePlan,
  executeIntelligentPlan,
  executeRobustPlan,
}
