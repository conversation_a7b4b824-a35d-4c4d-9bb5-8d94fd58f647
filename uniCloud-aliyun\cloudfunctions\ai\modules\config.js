// AI 配置和常量模块
// 包含豆包 AI 配置、工具注册表、SSE 消息类型等配置信息
// 统一管理所有配置项，便于维护和修改

/**
 * 豆包 AI 配置参数
 * 用于初始化 OpenAI 客户端，连接豆包 AI 服务
 */
const doubaoParams = {
  baseURL: 'https://ark.cn-beijing.volces.com/api/v3', // 豆包 AI API 基础地址
  apiKey: 'a8b8b8b8-b8b8-b8b8-b8b8-b8b8b8b8b8b8', // API 密钥（生产环境需替换）
  timeout: 30000, // 请求超时时间（毫秒）
}

/**
 * 工具注册表
 * 定义所有可用的云函数工具及其配置信息
 * 包括参数定义、执行时间估算、分类等元数据
 */
const TOOL_REGISTRY = {
  // 获取项目列表工具
  getProjects: {
    cloudFunction: 'dida-todo', // 对应的云函数名称
    method: 'getProjects', // 调用的方法名
    description: '获取项目列表', // 工具功能描述
    parameters: {
      filter: {
        type: 'string',
        required: false,
        description: '项目名称过滤关键词',
      },
    },
    metadata: {
      estimatedTime: 1500, // 预估执行时间（毫秒）
      category: 'query', // 工具分类：查询类
    },
  },
  getTasks: {
    cloudFunction: 'dida-todo',
    method: 'getTasks',
    description: '获取任务列表',
    parameters: {
      projectId: { type: 'string', required: false, description: '项目 ID' },
      completed: { type: 'boolean', required: false, description: '是否获取已完成任务' },
      limit: { type: 'number', required: false, description: '返回数量限制' },
    },
    metadata: {
      estimatedTime: 2000,
      category: 'query',
    },
  },
  createTask: {
    cloudFunction: 'dida-todo',
    method: 'createTask',
    description: '创建任务',
    parameters: {
      taskData: { type: 'object', required: true, description: '任务数据' },
    },
    metadata: {
      estimatedTime: 2500,
      category: 'action',
    },
  },
  getProject: {
    cloudFunction: 'dida-todo',
    method: 'getProject',
    description: '获取项目详情',
    parameters: {
      projectId: { type: 'string', required: true, description: '项目 ID' },
    },
    metadata: {
      estimatedTime: 1500,
      category: 'query',
    },
  },
}

/**
 * SSE 消息类型常量
 * 定义所有 SSE 推送消息的类型标识
 * 用于前端识别不同阶段的推送数据
 */
const SSE_MESSAGE_TYPES = {
  // 基础流程消息类型
  start: '开始生成回复', // 开始处理用户请求
  intent_type: '意图类型识别', // 识别到用户意图类型
  intent_content_start: '意图内容开始', // 开始推送意图内容
  intent_content_chunk: '意图内容块', // 意图内容数据块
  end: '结束', // 处理完成
  error: '错误', // 处理出错

  // 任务执行相关消息类型（V1.1 新增）
  execution_plan_start: '执行计划开始', // 开始执行任务计划
  execution_step: '执行步骤', // 当前执行步骤信息
  step_result: '步骤结果', // 步骤执行结果
  step_error: '步骤错误', // 步骤执行出错
  execution_complete: '执行完成', // 任务执行完成
  execution_failed: '执行失败', // 任务执行失败
}

/**
 * 默认系统提示词
 * 用于指导 AI 进行意图识别和内容提取
 * 定义了三种意图类型和标准输出格式
 */
const DEFAULT_SYSTEM_PROMPT = `你是一个专业的任务分析助手。你的主要职责是理解用户输入的内容，并判断用户的意图类型。

请分析用户输入内容，并将其归类为以下三种意图之一：
1. create_task: 当用户想要创建、添加、设置、安排一个新任务时
2. find_task: 当用户想要查询、搜索、查看已有的任务时
3. chat: 其他所有不属于创建任务或查找任务的内容，视为一般闲聊

分析完成后，必须严格按照以下格式输出结果：
「意图类型」：[意图类型代码，必须是 create_task、find_task 或 chat 之一]
「意图内容」：[如果是创建任务，提取出要创建的任务内容；如果是查找任务，提取出要查找的任务关键词；如果是闲聊，则回复用户问题]

注意：
- 分析要准确，不要混淆不同意图类型
- 只输出上述指定的两行内容，不要添加任何其他解释或内容
- 确保格式严格遵循示例，包括使用中文引号「」`

module.exports = {
  doubaoParams,
  TOOL_REGISTRY,
  SSE_MESSAGE_TYPES,
  DEFAULT_SYSTEM_PROMPT,
}
